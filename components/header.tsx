"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Scale, Menu, X } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  return (
    <header className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Scale className="h-9 w-9" />
            <span className="text-2xl font-bold">Find Car Accident Lawyers</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-6 text-lg">
            <Link href="/" className="hover:text-accent transition-colors">
              Home
            </Link>
            <Link href="/search" className="hover:text-accent transition-colors">
              Lawyers
            </Link>
            <div className="relative group">
              <button className="hover:text-accent transition-colors flex items-center">
                Free Tools
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  <Link href="/car-accident-settlement-calculator" className="block px-4 py-2 text-sm text-gray-700 hover:bg-accent/10 hover:text-black">
                    Settlement Calculator
                  </Link>
                  <Link href="/free-tools/ai-accident-photo-analyzer" className="block px-4 py-2 text-sm text-gray-700 hover:bg-accent/10 hover:text-black">
                    AI Accident Photo Analyzer
                  </Link>
                </div>
              </div>
            </div>
            <Link href="/blog" className="hover:text-accent transition-colors">
              Blog
            </Link>
            <Link href="/about" className="hover:text-accent transition-colors">
              About
            </Link>
            <Link href="/contact" className="hover:text-accent transition-colors">
              Contact
            </Link>
          </nav>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden text-primary-foreground hover:bg-primary-foreground/10"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-primary-foreground/20">
            <div className="flex flex-col space-y-4 pt-4">
              <Link
                href="/"
                className="hover:text-accent transition-colors text-lg"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/search"
                className="hover:text-accent transition-colors text-lg"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Lawyers
              </Link>
              <div className="space-y-2">
                <span className="text-sm font-medium text-primary-foreground/80">Free Tools</span>
                <div className="pl-4 space-y-2">
                  <Link
                    href="/car-accident-settlement-calculator"
                    className="hover:text-accent transition-colors text-base block"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Settlement Calculator
                  </Link>
                  <Link
                    href="/free-tools/ai-accident-photo-analyzer"
                    className="hover:text-accent transition-colors text-base block"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    AI Accident Photo Analyzer
                  </Link>
                </div>
              </div>
              <Link
                href="/blog"
                className="hover:text-accent transition-colors text-lg"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Blog
              </Link>
              <Link
                href="/about"
                className="hover:text-accent transition-colors text-lg"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                About
              </Link>
              <Link
                href="/contact"
                className="hover:text-accent transition-colors text-lg"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Contact
              </Link>
            </div>
          </nav>
        )}
      </div>
    </header>
  )
}
