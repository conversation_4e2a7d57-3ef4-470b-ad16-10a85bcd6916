"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, CheckCircle } from "lucide-react"
import { Camera, Upload, X, Loader2, DollarSign, Download } from "lucide-react"

interface AnalysisResult {
  damageSeverity: string
  estimatedCost: string
  faultIndicators: string[]
  confidence: number
  disclaimer: string
}

interface PartialAnalysisResult {
  damageSeverity?: string
  estimatedCost?: string
  faultIndicators?: string[]
  confidence?: number
  disclaimer: string
  isComplete: boolean
}

export function PhotoAnalyzer() {
  const [files, setFiles] = useState<File[]>([])
  const [previewUrls, setPreviewUrls] = useState<string[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)
  const [partialResult, setPartialResult] = useState<PartialAnalysisResult | null>(null)
  const [error, setError] = useState("")
  const [consentGiven, setConsentGiven] = useState(false)
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  // Load persisted data on component mount
  useEffect(() => {
    const savedResult = localStorage.getItem('photoAnalyzer_result')
    const savedConsent = localStorage.getItem('photoAnalyzer_consent')
    const savedPreviewUrls = localStorage.getItem('photoAnalyzer_previewUrls')

    if (savedResult) {
      try {
        setResult(JSON.parse(savedResult))
      } catch (e) {
        console.warn('Failed to parse saved result:', e)
      }
    }

    if (savedConsent) {
      setConsentGiven(JSON.parse(savedConsent))
    }

    if (savedPreviewUrls) {
      try {
        setPreviewUrls(JSON.parse(savedPreviewUrls))
      } catch (e) {
        console.warn('Failed to parse saved preview URLs:', e)
      }
    }
  }, [])

  // Save data to localStorage whenever it changes
  useEffect(() => {
    if (result) {
      localStorage.setItem('photoAnalyzer_result', JSON.stringify(result))
    }
  }, [result])

  useEffect(() => {
    localStorage.setItem('photoAnalyzer_consent', JSON.stringify(consentGiven))
  }, [consentGiven])

  useEffect(() => {
    if (previewUrls.length > 0) {
      localStorage.setItem('photoAnalyzer_previewUrls', JSON.stringify(previewUrls))
    }
  }, [previewUrls])

  const compressImage = (file: File, maxWidth: number = 1024, quality: number = 0.8): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }

        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            resolve(file) // Fallback to original if compression fails
          }
        }, 'image/jpeg', quality)
      }

      img.src = URL.createObjectURL(file)
    })
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    if (files.length + selectedFiles.length > 5) {
      setError("Maximum 5 images allowed")
      return
    }

    setError("Compressing images...")

    try {
      // Compress images
      const compressedFiles = await Promise.all(
        selectedFiles.slice(0, 5 - files.length).map(file => compressImage(file))
      )

      const newFiles = [...files, ...compressedFiles]
      setFiles(newFiles)

      const newPreviews = newFiles.map(file => URL.createObjectURL(file))
      setPreviewUrls(newPreviews)
      setError("")
    } catch (error) {
      console.error('Image compression failed:', error)
      setError("Image compression failed. Please try again.")
    }
  }

  const removeImage = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index)
    const newPreviews = previewUrls.filter((_, i) => i !== index)
    setFiles(newFiles)
    setPreviewUrls(newPreviews)
  }

  const clearAll = () => {
    if (window.confirm('Are you sure you want to clear all uploaded photos and analysis results? This action cannot be undone.')) {
      setFiles([])
      setPreviewUrls([])
      setResult(null)
      setError("")
      setConsentGiven(false)

      // Clear localStorage
      localStorage.removeItem('photoAnalyzer_result')
      localStorage.removeItem('photoAnalyzer_consent')
      localStorage.removeItem('photoAnalyzer_previewUrls')
    }
  }

  const analyzePhotos = async () => {
    if (files.length === 0) {
      setError("Please upload at least one photo")
      return
    }
    if (!consentGiven) {
      setError("Please give consent for image processing")
      return
    }

    setIsAnalyzing(true)
    setError("")
    setResult(null)

    // Show initial partial result immediately
    setPartialResult({
      disclaimer: "This analysis is for informational purposes only and is not legal or medical advice. All conclusions are estimates only. Please consult with qualified attorneys and insurance professionals for accurate assessments.",
      isComplete: false
    })

    try {
      const base64Images = await Promise.all(
        files.map(file => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader()
            reader.onload = () => resolve(reader.result as string)
            reader.readAsDataURL(file)
          })
        })
      )

      // Update partial result with processing status
      setPartialResult(prev => ({
        ...prev!,
        damageSeverity: "Analyzing damage patterns...",
        estimatedCost: "Calculating repair costs...",
        faultIndicators: ["Processing fault indicators..."],
        confidence: 0,
        isComplete: false
      }))

      const content = [
        {
          type: "text",
          text: `Analyze these car accident photos and provide a structured response in the following format:

DAMAGE SEVERITY: [minor/moderate/severe/catastrophic]
ESTIMATED COST: $[amount range]
FAULT INDICATORS:
- [indicator 1]
- [indicator 2]
- [indicator 3]
CONFIDENCE: [percentage]%

Provide detailed analysis for each section. For damage severity, classify as minor (cosmetic damage), moderate (functional damage), severe (structural damage), or catastrophic (total loss). For estimated cost, provide a realistic range in USD. For fault indicators, list specific observable evidence like skid marks, impact patterns, vehicle positioning, etc. IMPORTANT: Emphasize this is NOT legal or medical advice - users should consult professionals.`
        },
        ...base64Images.map(base64 => ({
          type: "image_url" as const,
          image_url: { url: base64 }
        }))
      ]

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${process.env.NEXT_PUBLIC_OPENROUTER_API_KEY}`,
          "HTTP-Referer": "https://findcaraccidentlawyers.org",
          "X-Title": "Find Car Accident Attorneys - AI Photo Analyzer",
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          model: "google/gemini-2.5-flash-lite",
          messages: [{
            role: "user",
            content
          }]
        })
      })

      if (!response.ok) {
        throw new Error("Analysis failed")
      }

      const data = await response.json()
      const analysisText = data.choices[0].message.content

      // Parse the response into structured format
      const rawConfidence = parseFloat(extractFromText(analysisText, /confidence.*?(\d+(?:\.\d+)?)/i, "75"))
      const parsedResult: AnalysisResult = {
        damageSeverity: extractFromText(analysisText, /damage\s+severity:\s*([^\n]+)/i, "Unable to determine"),
        estimatedCost: extractFromText(analysisText, /estimated\s+cost:\s*([^\n]+)/i, "Unable to estimate"),
        faultIndicators: extractListFromText(analysisText),
        confidence: clampConfidence(rawConfidence),
        disclaimer: "This analysis is for informational purposes only and is not legal or medical advice. All conclusions are estimates only. Please consult with qualified attorneys and insurance professionals for accurate assessments."
      }

      // Clear partial result and set final result
      setPartialResult(null)
      setResult(parsedResult)
    } catch (err) {
      setPartialResult(null)
      setError("Analysis failed. Please try again.")
      console.error(err)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const extractFromText = (text: string, regex: RegExp, fallback: string): string => {
    const match = text.match(regex)
    return match ? cleanTextFormatting(match[1].trim()) : fallback
  }

  const cleanTextFormatting = (text: string): string => {
    return text
      .replace(/\*\*/g, '') // Remove bold markdown
      .replace(/\*/g, '') // Remove italic markdown
      .replace(/^\s*[•\-\*]\s*/gm, '') // Remove bullet points
      .replace(/^\s*\d+\)\s*/gm, '') // Remove numbered lists
      .replace(/\n\s*\n/g, '\n') // Remove extra blank lines
      .replace(/\[([^\]]+)\]/g, '$1') // Remove square brackets but keep content
      .trim()
  }

  const clampConfidence = (confidence: number): number => {
    // Clamp confidence between 0 and 1
    return Math.max(0, Math.min(1, confidence / 100)) // Assuming AI might return percentages
  }

  const extractListFromText = (text: string): string[] => {
    // Look for fault indicators section
    const regex = /fault\s+indicators:\s*([\s\S]*?)(?=confidence|$)/i
    const match = text.match(regex)
    if (!match) return ['No specific fault indicators detected']

    // Split by line breaks and clean up
    const items = match[1]
      .split('\n')
      .map(item => cleanTextFormatting(item.trim()))
      .filter(item => item.length > 3 && !item.match(/^[\s\-\*•]+$/)) // Filter out very short items and separator lines
      .slice(0, 10) // Limit to 10 items max

    return items.length > 0 ? items : ['Unable to extract specific indicators']
  }

  const generatePDF = async () => {
    if (!result) return

    setIsGeneratingPDF(true)
    try {
      // Dynamic import to avoid SSR issues
      const jsPDF = (await import('jspdf')).default
      const html2canvas = (await import('html2canvas')).default

      const pdf = new jsPDF()
      const pageWidth = pdf.internal.pageSize.getWidth()
      const pageHeight = pdf.internal.pageSize.getHeight()
      const margin = 20
      const footerHeight = 25
      const usableHeight = pageHeight - footerHeight - 40 // Leave space for header and footer

      let currentPage = 1
      let yPosition = 40

      // Helper function to add footer with page numbers
      const addFooter = (pageNum: number, totalPages: number) => {
        pdf.setFontSize(12)
        pdf.setTextColor(100, 100, 100)
        pdf.text('findcaraccidentlawyers.org', pageWidth / 2, pageHeight - 15, { align: 'center' })
        pdf.setFontSize(10)
        pdf.text(`Page ${pageNum}/${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' })
      }

      // Helper function to check if we need a new page
      const checkNewPage = (requiredSpace: number) => {
        if (yPosition + requiredSpace > usableHeight) {
          currentPage++
          pdf.addPage()
          yPosition = 40
          return true
        }
        return false
      }

      // Header
      pdf.setFontSize(20)
      pdf.setTextColor(0, 0, 0)
      pdf.text('AI Accident Photo Analysis Report', pageWidth / 2, 30, { align: 'center' })

      // Date
      pdf.setFontSize(12)
      pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, margin, yPosition)
      yPosition += 20

      // Images Section
      if (files.length > 0) {
        checkNewPage(30)
        pdf.setFontSize(16)
        pdf.setTextColor(0, 0, 0)
        pdf.text('Uploaded Images:', margin, yPosition)
        yPosition += 15

        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          try {
            // Convert file to base64 for PDF
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            const img = new Image()

            await new Promise((resolve, reject) => {
              img.onload = () => {
                // Calculate dimensions to fit within PDF page with higher quality
                const maxWidth = pageWidth - (margin * 2)
                const maxHeight = 120 // Increased from 80 for better visibility
                let { width, height } = img

                // Calculate scale factor for high-resolution rendering
                const scaleFactor = 2 // Render at 2x resolution for better quality

                if (width > maxWidth) {
                  height = (height * maxWidth) / width
                  width = maxWidth
                }
                if (height > maxHeight) {
                  width = (width * maxHeight) / height
                  height = maxHeight
                }

                // Set canvas to high resolution
                canvas.width = width * scaleFactor
                canvas.height = height * scaleFactor

                // Scale the context to match
                ctx!.scale(scaleFactor, scaleFactor)

                // Enable image smoothing for better quality
                ctx!.imageSmoothingEnabled = true
                ctx!.imageSmoothingQuality = 'high'

                // Draw image at high resolution
                ctx?.drawImage(img, 0, 0, width, height)

                checkNewPage(height + 20)

                // Add image to PDF with higher quality settings
                const imgData = canvas.toDataURL('image/jpeg', 0.95) // Increased quality from 0.8 to 0.95
                pdf.addImage(imgData, 'JPEG', margin, yPosition, width, height)
                yPosition += height + 10

                resolve(true)
              }
              img.onerror = reject
              img.src = previewUrls[i]
            })
          } catch (error) {
            console.warn(`Failed to add image ${i + 1} to PDF:`, error)
          }
        }
        yPosition += 10
      }

      // Analysis Results
      checkNewPage(40)
      pdf.setFontSize(18)
      pdf.setTextColor(0, 0, 0)
      pdf.text('Analysis Results', margin, yPosition)
      yPosition += 20

      // Damage Severity
      checkNewPage(30)
      pdf.setFontSize(16)
      pdf.text('Damage Severity:', margin, yPosition)
      yPosition += 10
      pdf.setFontSize(12)
      const severityLines = pdf.splitTextToSize(result.damageSeverity, pageWidth - (margin * 2))
      pdf.text(severityLines, margin, yPosition)
      yPosition += severityLines.length * 7 + 10

      // Confidence
      checkNewPage(15)
      pdf.text(`Analysis Confidence: ${(result.confidence * 100).toFixed(0)}%`, margin, yPosition)
      yPosition += 15

      // Estimated Cost
      checkNewPage(30)
      pdf.setFontSize(16)
      pdf.text('Estimated Repair Cost:', margin, yPosition)
      yPosition += 10
      pdf.setFontSize(12)
      const costLines = pdf.splitTextToSize(result.estimatedCost, pageWidth - (margin * 2))
      pdf.text(costLines, margin, yPosition)
      yPosition += costLines.length * 7 + 15

      // Fault Indicators
      checkNewPage(30)
      pdf.setFontSize(16)
      pdf.text('Fault Indicators:', margin, yPosition)
      yPosition += 15
      pdf.setFontSize(12)
      result.faultIndicators.forEach((indicator) => {
        checkNewPage(15)
        const lines = pdf.splitTextToSize(`• ${indicator}`, pageWidth - (margin * 2))
        pdf.text(lines, margin, yPosition)
        yPosition += lines.length * 7 + 5
      })

      // Disclaimer
      yPosition += 10
      checkNewPage(50)
      pdf.setFontSize(14)
      pdf.setTextColor(200, 0, 0)
      pdf.text('Important Disclaimer:', margin, yPosition)
      yPosition += 15
      pdf.setFontSize(10)
      pdf.setTextColor(0, 0, 0)
      const disclaimerText = 'This analysis is for informational purposes only and is not legal or medical advice. All conclusions are estimates only. Please consult with qualified attorneys and insurance professionals for accurate assessments.'
      const disclaimerLines = pdf.splitTextToSize(disclaimerText, pageWidth - (margin * 2))
      pdf.text(disclaimerLines, margin, yPosition)

      // Add footers to all pages
      const totalPages = pdf.internal.getNumberOfPages()
      for (let i = 1; i <= totalPages; i++) {
        pdf.setPage(i)
        addFooter(i, totalPages)
      }

      // Save the PDF
      pdf.save('accident-analysis-report.pdf')
    } catch (error) {
      console.error('Error generating PDF:', error)
      setError('Failed to generate PDF. Please try again.')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle>Upload Accident Photos</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="file-upload" className="cursor-pointer">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-accent transition-colors">
                  <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <div className="text-lg font-medium">Drag & drop photos here</div>
                  <div className="text-sm text-muted-foreground mt-2">Or click to browse files</div>
                </div>
              </Label>
              <Input
                id="file-upload"
                type="file"
                multiple
                accept="image/*"
                className="hidden"
                onChange={handleFileSelect}
              />
            </div>
            <div>
              <Label htmlFor="camera-capture" className="cursor-pointer">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-accent transition-colors">
                  <Camera className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <div className="text-lg font-medium">Take a Photo</div>
                  <div className="text-sm text-muted-foreground mt-2">Open camera on mobile</div>
                </div>
              </Label>
              <input
                id="camera-capture"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => {
                  const selectedFiles = Array.from(e.target.files || [])
                  if (selectedFiles.length > 0) {
                    handleFileSelect({target: {files: selectedFiles}} as unknown as React.ChangeEvent<HTMLInputElement>)
                  }
                }}
              />
            </div>
          </div>

          {previewUrls.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {previewUrls.map((url, index) => (
                <div key={index} className="relative">
                  <img src={url} alt={`Preview ${index + 1}`} className="w-full h-24 object-cover rounded-lg" />
                  <button
                    onClick={() => removeImage(index)}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            Upload up to 5 photos (JPG, PNG). Include overall scene, damage details, skid marks, and road conditions for best results.
          </div>
        </CardContent>
      </Card>

      {/* Consent Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            <input
              id="consent"
              type="checkbox"
              checked={consentGiven}
              onChange={(e) => setConsentGiven(e.target.checked)}
              className="mr-2 h-4 w-4"
            />
            <Label htmlFor="consent" className="text-sm">
              I consent to processing these images for AI analysis. Images are not stored permanently and processing complies with our Privacy Policy.
            </Label>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4">
        <Button
          onClick={analyzePhotos}
          disabled={files.length === 0 || !consentGiven || isAnalyzing}
          size="lg"
          className="px-12"
        >
          {isAnalyzing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Analyzing Photos...
            </>
          ) : (
            "Analyze Photos (AI)"
          )}
        </Button>
        <Button
          onClick={clearAll}
          variant="outline"
          size="lg"
          className="px-8"
          disabled={isAnalyzing}
        >
          Clear All
        </Button>
      </div>

      {/* Error */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <span className="font-semibold text-red-700">Error</span>
          </div>
          <p className="mt-2 text-red-600">{error}</p>
        </div>
      )}

      {/* Results */}
      {(result || partialResult) && (
        <Card className="mt-8">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Analysis Results</CardTitle>
              <Button
                onClick={generatePDF}
                disabled={isGeneratingPDF}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                {isGeneratingPDF ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Generating PDF...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Save as PDF
                  </>
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                <span className="font-semibold text-red-700">Important Disclaimer</span>
              </div>
              <p className="mt-2 text-red-600">{(result || partialResult)?.disclaimer}</p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="font-semibold mb-3 text-blue-800 flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  Damage Severity
                </h3>
                <p className="text-lg font-medium text-blue-900 mb-3">
                  {(result || partialResult)?.damageSeverity || "Analyzing..."}
                  {partialResult && !partialResult.isComplete && (
                    <span className="ml-2 text-sm text-blue-600 animate-pulse">Processing...</span>
                  )}
                </p>
                <div className="mt-2">
                  <div className="flex justify-between text-sm text-blue-700 mb-1">
                    <span>Analysis Confidence</span>
                    <span>{(((result || partialResult)?.confidence || 0) * 100).toFixed(0)}%</span>
                  </div>
                  <div className="bg-blue-200 h-2 rounded-full">
                    <div className="bg-blue-600 h-2 rounded-full transition-all duration-300" style={{ width: `${((result || partialResult)?.confidence || 0) * 100}%` }}></div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-semibold mb-3 text-green-800 flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Estimated Repair Cost
                </h3>
                <p className="text-lg font-medium text-green-900">
                  {(result || partialResult)?.estimatedCost || "Calculating..."}
                  {partialResult && !partialResult.isComplete && (
                    <span className="ml-2 text-sm text-green-600 animate-pulse">Processing...</span>
                  )}
                </p>
                <p className="text-sm text-green-700 mt-2">
                  *Estimates may vary based on location, parts availability, and labor costs
                </p>
              </div>
            </div>

            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h3 className="font-semibold mb-3 text-orange-800 flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                Fault Indicators Detected
              </h3>
              <div className="space-y-2">
                {((result || partialResult)?.faultIndicators || []).map((indicator, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-orange-900 text-sm leading-relaxed">
                      {indicator}
                      {partialResult && !partialResult.isComplete && indicator.includes("Processing") && (
                        <span className="ml-2 animate-pulse">⏳</span>
                      )}
                    </p>
                  </div>
                ))}
              </div>
              <p className="text-xs text-orange-700 mt-3 italic">
                These indicators are observational only and do not constitute legal determination of fault.
              </p>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-blue-500 mr-2" />
                <span className="font-semibold text-blue-700">Next Steps</span>
              </div>
              <p className="mt-2 text-blue-600">
                Share these results with your attorney or insurance adjuster for proper evaluation. Book a consultation with one of our verified lawyers to discuss your case.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
