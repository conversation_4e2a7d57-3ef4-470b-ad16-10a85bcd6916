import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { addDirectoryUTM } from "@/lib/utils/utm"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Star,
  Heart,
  MessageSquare,
  Share2,
  Phone,
  MapPin,
  Globe,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  CheckCircle,
} from "lucide-react"
import Image from "next/image"
import { getAttorneys } from "@/lib/supabase"

export const revalidate = 3600; // Revalidate every hour for ISR

export async function generateStaticParams() {
  const { attorneys } = await getAttorneys(1, 1000); // Fetch all or paginate if needed
  return attorneys.map((attorney) => ({
    id: attorney.source_id
  }));
}

interface LawyerPageProps {
  params: Promise<{ id: string }>
}

export async function generateMetadata({ params }: LawyerPageProps): Promise<Metadata> {
  const { id } = await params;
  // For now, use default metadata
  return {
    title: 'Lawyer Profile',
    description: 'View lawyer profile and contact information.',
  };
}

export default async function LawyerDetailPage({ params }: LawyerPageProps) {
  const { id } = await params;
  // For demo, use hardcoded data; in production, fetch by id
  const lawyerData = {
    source_id: "04e46dc5-af24-4204-b8bf-75bbf0328a1c",
    title: "Frank Azar Car & Truck Accident Lawyers",
    subtitle: "Personal injury, car accident specialists",
    category_name: "Personal injury attorney",
    address: "1730 S College Ave Unit 202, Fort Collins, CO 80525",
    city: "Fort Collins",
    state: "Colorado",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://www.fdazar.com/fort-collins-car-accident-attorney/",
    total_score: 4.9,
    reviews_count: 266,
    hero_image: "/professional-law-office-exterior.png",
    profile_image: "/attorney-headshot.png",
    opening_hours: [
      { day: "Monday", hours: "5:00 AM - 7:00 PM" },
      { day: "Tuesday", hours: "5:00 AM - 7:00 PM" },
      { day: "Wednesday", hours: "5:00 AM - 7:00 PM" },
      { day: "Thursday", hours: "5:00 AM - 7:00 PM" },
      { day: "Friday", hours: "5:00 AM - 7:00 PM" },
      { day: "Saturday", hours: "5:00 AM - 7:00 PM" },
      { day: "Sunday", hours: "Closed All Day" },
    ],
    description:
      "At Frank Azar Car & Truck Accident Lawyers, we specialize in personal injury cases with a focus on car and truck accidents. Our experienced team provides compassionate, knowledgeable, and dedicated legal representation to help you get the compensation you deserve. We understand the physical, emotional, and financial toll that accidents can take on victims and their families.\n\nOur firm has successfully handled thousands of personal injury cases, securing millions in settlements and verdicts for our clients. We work on a contingency fee basis, meaning you don't pay unless we win your case. Our attorneys are available 24/7 to provide immediate assistance and guidance during this difficult time.",
    amenities: [
      "Free Consultation",
      "24/7 Availability",
      "No Win No Fee",
      "Multilingual Staff",
      "Home/Hospital Visits",
      "Insurance Negotiations",
      "Trial Experience",
      "Medical Referrals",
      "Case Investigation",
      "Settlement Negotiations",
      "Court Representation",
      "Accident Reconstruction",
    ],
    processed_reviews: {
      service_keywords: [
        "Car accident",
        "Personal injury",
        "Settlement",
        "Legal representation",
        "Insurance claims",
        "Compensation",
      ],
    },
  };

  const activeTab = "description";

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-5 w-5 ${i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
      />
    ))
  }

  const getCurrentStatus = () => {
    const now = new Date()
    const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.
    const currentHour = now.getHours()

    // For demo purposes, showing as open
    return { isOpen: true, text: "Open", hours: "5:00 am - 7:00 pm" }
  }

  const status = getCurrentStatus()

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <div className="relative h-80 overflow-hidden">
        <Image src={lawyerData.hero_image || "/placeholder.svg"} alt={lawyerData.title} fill className="object-cover" />
        <div className="absolute inset-0 bg-black/40" />

        {/* Overlay Content */}
        <div className="absolute inset-0 flex items-center">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between text-white">
              <div className="flex items-center gap-6">
                <div className="relative w-20 h-20 rounded-full overflow-hidden border-4 border-white">
                  <Image
                    src={lawyerData.profile_image || "/placeholder.svg"}
                    alt={lawyerData.title}
                    fill
                    className="object-cover"
                  />
                </div>

                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-3xl font-bold">{lawyerData.title}</h1>
                    <Badge variant="secondary" className="bg-green-500 text-white">
                      ✓
                    </Badge>
                  </div>
                  <p className="text-lg opacity-90 mb-2">{lawyerData.subtitle}</p>

                  <div className="flex items-center gap-6 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">{renderStars(lawyerData.total_score)}</div>
                      <span className="font-semibold">{lawyerData.total_score}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      <span className="font-semibold">{lawyerData.phone}</span>
                      <Badge variant="destructive" className="text-xs">
                        SHOW
                      </Badge>
                    </div>

                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>{lawyerData.address}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button variant="secondary" size="sm">
                  <Heart className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button variant="secondary" size="sm">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Submit Review
                </Button>
                <Button variant="secondary" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs value={activeTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="description">Description</TabsTrigger>
                <TabsTrigger value="business-info">Business Info</TabsTrigger>
                <TabsTrigger value="directions">Get Directions</TabsTrigger>
              </TabsList>

              <TabsContent value="description" className="mt-6">
                {/* Law Firm Image */}
                <div className="mb-8">
                  <div className="relative h-96 w-full rounded-lg overflow-hidden">
                    <Image
                      src={lawyerData.hero_image}
                      alt={`${lawyerData.title} - Law Firm Office`}
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                </div>

                {/* Maps Section */}
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-semibold">Maps</h3>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        🗺️ Map
                      </Button>
                      <Button variant="outline" size="sm">
                        🏠 Street View
                      </Button>
                      <Button variant="outline" size="sm">
                        🧭 Get Directions
                      </Button>
                    </div>
                  </div>

                  <div className="relative h-64 bg-gray-200 rounded-lg overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                      Interactive Map Placeholder
                    </div>
                  </div>
                </div>

                {/* Amenities */}
                <div className="mt-8">
                  <h3 className="text-xl font-semibold mb-4">Listing Amenities</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {lawyerData.amenities.map((amenity, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{amenity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="business-info" className="mt-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Contact Information</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-3">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{lawyerData.phone}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Globe className="h-4 w-4 text-muted-foreground" />
                        <a
                          href={lawyerData.website}
                          className="text-blue-600 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {lawyerData.website}
                        </a>
                      </div>
                      <div className="flex items-center gap-3">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>{lawyerData.address}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3">Practice Areas</h3>
                    <div className="flex flex-wrap gap-2">
                      {lawyerData.processed_reviews.service_keywords.map((keyword, index) => (
                        <Badge key={index} variant="outline">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="directions" className="mt-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Get Directions</h3>
                  <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                      Interactive Directions Map Placeholder
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Business Info Card */}
            <div className="bg-white rounded-lg border p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{lawyerData.address}</span>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{lawyerData.phone}</span>
                  <Badge variant="destructive" className="text-xs">
                    SHOW
                  </Badge>
                </div>

                <div className="flex items-center gap-3">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <a
                    href={addDirectoryUTM(lawyerData.website)}
                    className="text-sm text-blue-600 hover:underline"
                    target="_blank"
                    rel="noopener noreferrer"
                    data-umami-event="link-click"
                    data-umami-event-url={lawyerData.website}
                  >
                    fdazar.com
                  </a>
                </div>

                <div className="flex items-center gap-3">
                  <span className="text-sm">📧</span>
                  <span className="text-sm">{lawyerData.email}</span>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-semibold mb-3">Follow Us</h4>
                <div className="flex gap-3">
                  <Button variant="outline" size="sm">
                    <Facebook className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Twitter className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Instagram className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Linkedin className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Hours */}
            <div className="bg-white rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold">📅 Today</h4>
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${status.isOpen ? "text-green-600" : "text-red-600"}`}>
                    {status.text}
                  </span>
                  <span className="text-sm text-muted-foreground">{status.hours}</span>
                </div>
              </div>

              <div className="space-y-2">
                {lawyerData.opening_hours.map((day, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className={day.day === "Tuesday" ? "font-medium" : "text-muted-foreground"}>{day.day}</span>
                    <span className={day.hours === "Closed All Day" ? "text-red-600" : "text-muted-foreground"}>
                      {day.hours}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Nearby Listings */}
            <div className="bg-white rounded-lg border p-6">
              <h4 className="font-semibold mb-4">📍 Nearby listings</h4>
              <p className="text-sm text-muted-foreground">
                Find more <span className="text-blue-600">Personal injury attorney</span> near{" "}
                <span className="font-medium">Frank Azar Car & Truck Accident Lawyers</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
