import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Breadcrumb } from "@/components/breadcrumb"
import { SchemaMarkup } from "@/components/schema-markup"
import { LawyerCard } from "@/components/lawyer-card"
import { getAttorneysByLocation, getCitiesByState, getState<PERSON>, Attorney } from "@/lib/supabase"
import { generateBreadcrumbs, generateStateMetaTitle, generateStateMetaDescription, generateBreadcrumbStructuredData } from "@/lib/utils/seo"
import Link from 'next/link'
import { generateCitySlug, generateStateSlug } from "@/lib/utils/seo"

export const revalidate = 3600; // Revalidate every hour for ISR

export async function generateStaticParams() {
  const { states } = await getStates();
  return states.map((state) => ({
    state: generateStateSlug(state)
  }));
}

interface StatePageProps {
  params: Promise<{
    state: string
  }>
}

export async function generateMetadata({ params }: StatePageProps): Promise<Metadata> {
  const { state } = await params
  const stateName = decodeURIComponent(state).replace(/-/g, ' ')
  const { attorneys } = await getAttorneysByLocation(stateName)

  return {
    title: generateStateMetaTitle(stateName, attorneys.length),
    description: generateStateMetaDescription(stateName, attorneys.length),
    alternates: {
      canonical: `/${state}`,
    },
    openGraph: {
      title: generateStateMetaTitle(stateName, attorneys.length),
      description: generateStateMetaDescription(stateName, attorneys.length),
      url: `https://findcaraccidentlawyers.org/${state}`,
    },
  }
}

export default async function StatePage({ params }: StatePageProps) {
  const { state } = await params
  const stateName = decodeURIComponent(state).replace(/-/g, ' ')
  const { attorneys, error } = await getAttorneysByLocation(stateName)
  const { cities } = await getCitiesByState(stateName)

  if (error || attorneys.length === 0) {
    notFound()
  }

  const breadcrumbs = generateBreadcrumbs(undefined, undefined, stateName)
  const breadcrumbSchema = generateBreadcrumbStructuredData(breadcrumbs)

  // Group attorneys by city
  const attorneysByCity = attorneys.reduce((acc, attorney) => {
    const city = attorney.city
    if (!acc[city]) {
      acc[city] = []
    }
    acc[city].push(attorney)
    return acc
  }, {} as Record<string, Attorney[]>)

  const stateSchema = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: `Car Accident Attorneys in ${stateName}`,
    description: `Find experienced car accident attorneys throughout ${stateName}. Browse ${attorneys.length} verified attorneys with reviews and ratings.`,
    url: `https://findcaraccidentlawyers.org/${state}`,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: attorneys.length,
      itemListElement: attorneys.slice(0, 10).map((attorney, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Attorney',
          name: attorney.title,
          address: {
            '@type': 'PostalAddress',
            addressLocality: attorney.city,
            addressRegion: attorney.state
          },
          aggregateRating: attorney.reviews_count > 0 ? {
            '@type': 'AggregateRating',
            ratingValue: attorney.total_score,
            reviewCount: attorney.reviews_count
          } : undefined
        }
      }))
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <SchemaMarkup data={[breadcrumbSchema, stateSchema]} />
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <Breadcrumb items={breadcrumbs} />
        
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">
            Car Accident Attorneys in {stateName}
          </h1>
          <p className="text-lg text-muted-foreground mb-6">
            Find experienced car accident attorneys throughout {stateName}. Browse {attorneys.length} verified attorneys with reviews and ratings.
          </p>
        </div>

        {/* Cities in this state */}
        {cities.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">Browse by City</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {cities.slice(0, 20).map((city) => {
                const cityAttorneys = attorneysByCity[city] || []
                return (
                  <Link
                    key={city}
                    href={`/${generateStateSlug(stateName)}/${generateCitySlug(city)}`}
                    className="block p-4 bg-card rounded-lg border hover:shadow-md transition-shadow duration-200"
                  >
                    <div className="text-center">
                      <span className="font-medium text-foreground hover:text-primary transition-colors block">
                        {city}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {cityAttorneys.length} attorney{cityAttorneys.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </Link>
                )
              })}
            </div>
          </section>
        )}

        {/* Top attorneys in state */}
        <section>
          <h2 className="text-2xl font-semibold mb-6">Top-Rated Attorneys in {stateName}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {attorneys.slice(0, 9).map((attorney) => (
              <LawyerCard key={attorney.source_id} lawyer={attorney} />
            ))}
          </div>

          {attorneys.length > 9 && (
            <div className="text-center mt-8">
              <p className="text-muted-foreground">
                Showing 9 of {attorneys.length} attorneys in {stateName}
              </p>
              <a href={`/${state}/page/1`} className="inline-block mt-2 text-primary hover:underline">
                View all attorneys in {stateName}
              </a>
            </div>
          )}
        </section>
      </main>
      
      <Footer />
    </div>
  )
}
