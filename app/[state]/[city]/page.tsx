import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Breadcrumb } from "@/components/breadcrumb"
import { SchemaMarkup } from "@/components/schema-markup"
import { LawyerCard } from "@/components/lawyer-card"
import { PaginationComponent } from "@/components/pagination"
import { getAttorneysByLocation, getStates, getCitiesByState } from "@/lib/supabase"
import { generateBreadcrumbs, generateCityMetaTitle, generateCityMetaDescription, generateBreadcrumbStructuredData } from "@/lib/utils/seo"
import { getRecordsPerPage } from "@/lib/utils/pagination"
import { generateCitySlug, generateStateSlug } from "@/lib/utils/seo"

export const revalidate = 3600; // Revalidate every hour for ISR

export async function generateStaticParams() {
  const { states } = await getStates();
  const params = [];
  for (const state of states) {
    const { cities } = await getCitiesByState(state);
    for (const city of cities) {
      params.push({
        state: generateStateSlug(state),
        city: generateCitySlug(city)
      });
    }
  }
  return params;
}

interface CityPageProps {
  params: Promise<{
    state: string
    city: string
  }>
  searchParams: Promise<{
    page?: string
  }>
}

export async function generateMetadata({ params }: CityPageProps): Promise<Metadata> {
  const { state, city } = await params
  const stateName = decodeURIComponent(state).replace(/-/g, ' ')
  const cityName = decodeURIComponent(city).replace(/-/g, ' ')
  const { attorneys } = await getAttorneysByLocation(stateName, cityName)

  return {
    title: generateCityMetaTitle(cityName, stateName, attorneys.length),
    description: generateCityMetaDescription(cityName, stateName, attorneys.length),
    alternates: {
      canonical: `/${state}/${city}`,
    },
    openGraph: {
      title: generateCityMetaTitle(cityName, stateName, attorneys.length),
      description: generateCityMetaDescription(cityName, stateName, attorneys.length),
      url: `https://findcaraccidentlawyers.org/${state}/${city}`,
    },
  }
}

export default async function CityPage({ params, searchParams }: CityPageProps) {
  const { state, city } = await params
  const { page } = await searchParams
  const stateName = decodeURIComponent(state).replace(/-/g, ' ')
  const cityName = decodeURIComponent(city).replace(/-/g, ' ')
  const currentPage = parseInt(page || '1', 10)
  const recordsPerPage = getRecordsPerPage()

  const { attorneys, error } = await getAttorneysByLocation(stateName, cityName)

  if (error || attorneys.length === 0) {
    notFound()
  }

  // Pagination
  const totalPages = Math.ceil(attorneys.length / recordsPerPage)
  const startIndex = (currentPage - 1) * recordsPerPage
  const endIndex = startIndex + recordsPerPage
  const paginatedAttorneys = attorneys.slice(startIndex, endIndex)

  const breadcrumbs = generateBreadcrumbs(undefined, cityName, stateName)
  const breadcrumbSchema = generateBreadcrumbStructuredData(breadcrumbs)

  const citySchema = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: `Car Accident Attorneys in ${cityName}, ${stateName}`,
    description: `Find the best car accident attorneys in ${cityName}, ${stateName}. Browse ${attorneys.length} verified attorneys with reviews and ratings.`,
    url: `https://findcaraccidentlawyers.org/${state}/${city}`,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: attorneys.length,
      itemListElement: paginatedAttorneys.map((attorney, index) => ({
        '@type': 'ListItem',
        position: startIndex + index + 1,
        item: {
          '@type': 'Attorney',
          name: attorney.title,
          address: {
            '@type': 'PostalAddress',
            addressLocality: attorney.city,
            addressRegion: attorney.state
          },
          aggregateRating: attorney.reviews_count > 0 ? {
            '@type': 'AggregateRating',
            ratingValue: attorney.total_score,
            reviewCount: attorney.reviews_count
          } : undefined
        }
      }))
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <SchemaMarkup data={[breadcrumbSchema, citySchema]} />
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <Breadcrumb items={breadcrumbs} />
        
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">
            Best Car Accident Attorneys in {cityName}, {stateName}
          </h1>
          <p className="text-lg text-muted-foreground mb-6">
            Find the best car accident attorneys in {cityName}, {stateName}. Browse {attorneys.length} verified attorneys with reviews, ratings, and contact information.
          </p>
        </div>

        {/* Results summary */}
        <div className="mb-6">
          <p className="text-muted-foreground">
            Showing {startIndex + 1}-{Math.min(endIndex, attorneys.length)} of {attorneys.length} attorneys in {cityName}, {stateName}
          </p>
        </div>

        {/* Attorneys grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {paginatedAttorneys.map((attorney) => (
            <LawyerCard key={attorney.source_id} lawyer={attorney} />
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <PaginationComponent
            currentPage={currentPage}
            totalPages={totalPages}
            baseUrl={`/${state}/${city}`}
          />
        )}

        {/* SEO content */}
        <section className="mt-12 prose max-w-none">
          <h2>Why Choose a Car Accident Attorney in {cityName}, {stateName}?</h2>
          <p>
            If you've been involved in a car accident in {cityName}, {stateName}, it's crucial to have experienced legal representation on your side. 
            Our directory features top-rated car accident attorneys who understand local laws and have a proven track record of success.
          </p>
          
          <h3>What to Look for in a Car Accident Attorney</h3>
          <ul>
            <li>Experience handling car accident cases in {stateName}</li>
            <li>Strong track record of successful settlements and verdicts</li>
            <li>Positive client reviews and testimonials</li>
            <li>No upfront fees - most work on a contingency basis</li>
            <li>Local knowledge of {cityName} courts and procedures</li>
          </ul>

          <h3>Common Types of Car Accident Cases</h3>
          <p>
            Car accident attorneys in {cityName} handle various types of cases including rear-end collisions, 
            intersection accidents, drunk driving accidents, distracted driving cases, and more. They can help you 
            recover compensation for medical bills, lost wages, pain and suffering, and property damage.
          </p>
        </section>
      </main>
      
      <Footer />
    </div>
  )
}
