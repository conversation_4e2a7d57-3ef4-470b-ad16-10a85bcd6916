import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Breadcrumb } from "@/components/breadcrumb"
import { SchemaMarkup } from "@/components/schema-markup"
import { AttorneyProfile } from "@/components/attorney-profile"
import { getAttorneyBySlug } from "@/lib/supabase"
import { generateBreadcrumbs, generateMetaTitle, generateMetaDescription, generateStructuredData, generateBreadcrumbStructuredData } from "@/lib/utils/seo"

interface AttorneyPageProps {
  params: Promise<{
    state: string
    city: string
    slug: string
  }>
}

export async function generateMetadata({ params }: AttorneyPageProps): Promise<Metadata> {
  const { slug } = await params
  const { attorney } = await getAttorneyBySlug(slug)

  if (!attorney) {
    return {
      title: 'Attorney Not Found',
      description: 'The requested attorney profile could not be found.'
    }
  }

  const { state, city } = await params

  return {
    title: generateMeta<PERSON><PERSON><PERSON>(attorney),
    description: generate<PERSON>eta<PERSON><PERSON><PERSON>(attorney),
    alternates: {
      canonical: `/${state}/${city}/${slug}`,
    },
    openGraph: {
      title: generateMetaTitle(attorney),
      description: generateMetaDescription(attorney),
      url: `https://findcaraccidentlawyers.org/${state}/${city}/${slug}`,
      type: 'profile',
    },
  }
}

export default async function AttorneyPage({ params }: AttorneyPageProps) {
  const { slug } = await params
  const { attorney, error } = await getAttorneyBySlug(slug)

  if (error || !attorney) {
    notFound()
  }

  const breadcrumbs = generateBreadcrumbs(attorney, attorney.city, attorney.state)
  const breadcrumbSchema = generateBreadcrumbStructuredData(breadcrumbs)
  const attorneySchema = generateStructuredData(attorney)

  return (
    <div className="min-h-screen bg-background">
      <SchemaMarkup data={[breadcrumbSchema, attorneySchema]} />
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <Breadcrumb items={breadcrumbs} />
        <AttorneyProfile attorney={attorney} />
      </main>
      
      <Footer />
    </div>
  )
}
