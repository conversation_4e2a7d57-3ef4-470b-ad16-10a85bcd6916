import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { generateStateSlug, generateCitySlug } from '@/lib/utils/seo'

// Helper function to generate attorney slug from title
function generateAttorneySlugFromTitle(title: string): string {
  if (!title) return 'unknown'
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://findcaraccidentlawyers.org'

    if (!supabase) {
      console.error('Supabase client is not initialized.')
      return new NextResponse('Error generating attorneys sitemap', { status: 500 })
    }

    const { data: attorneys, error } = await supabase
      .from('findcaraccidentattorneys-clean')
      .select('source_id, title, address, city, state, street, postal_code, country_code, lat, lng, total_score, reviews_count, last_reviews_update')
      .eq('reviews_processed', true)
      .order('total_score', { ascending: false })

    if (attorneys) {
      attorneys.forEach(attorney => {
        if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(attorney.source_id)) {
          console.error('Invalid UUID for source_id:', attorney.source_id)
          attorney.source_id = null
        }
      })
    }

    if (error) {
      console.error('Error fetching attorneys for sitemap:', error)
      return new NextResponse('Error generating attorneys sitemap', { status: 500 })
    }

    // Get unique states and cities
    const states = [...new Set(attorneys.map(a => a.state).filter(s => s))].sort()
    const cities = [...new Set(attorneys.map(a => a.city && a.state ? `${a.city}, ${a.state}` : null).filter(c => c))].sort()

    const currentDate = new Date().toISOString()

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`

    // Add state pages
    states.forEach(state => {
      if (!state) return
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(state)}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>
`
    })

    // Add city pages
    cities.forEach(cityState => {
      if (!cityState) return
      const [city, state] = cityState.split(', ')
      if (!city || !state) return
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(state)}/${generateCitySlug(city)}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
`
    })

    // Add attorney profile pages
    attorneys.forEach(attorney => {
      if (!attorney.state || !attorney.city || !attorney.title) {
        console.warn('Skipping attorney with missing data:', attorney.source_id)
        return
      }
      const lastmod = attorney.last_reviews_update || currentDate
      const attorneySlug = generateAttorneySlugFromTitle(attorney.title) || 'unknown'
      sitemap += `  <url>
    <loc>${baseUrl}/${generateStateSlug(attorney.state)}/${generateCitySlug(attorney.city)}/${attorneySlug}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>
`
    })

    sitemap += `</urlset>`

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    })
  } catch (error) {
    console.error('Error generating attorneys sitemap:', error)
    return new NextResponse('Error generating attorneys sitemap', { status: 500 })
  }
}
