import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://findcaraccidentlawyers.org'

    if (!supabase) {
      console.error('Supabase client is not initialized.')
      return new NextResponse('Error generating blogs sitemap', { status: 500 })
    }

    const { data: blogs, error: blogError } = await supabase
      .from('findcaraccidentattorneys-blog')
      .select('slug, title, published_date, updated_at')
      .lte('published_date', new Date().toISOString())
      .order('published_date', { ascending: false })

    if (blogError) {
      console.error('Error fetching blogs for sitemap:', blogError)
      return new NextResponse('Error generating blogs sitemap', { status: 500 })
    }

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`

    // Add blog posts
    if (blogs && blogs.length > 0) {
      blogs.forEach(blog => {
        const lastmod = blog.updated_at || blog.published_date || new Date().toISOString()
        sitemap += `  <url>
    <loc>${baseUrl}/blog/${blog.slug}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
`
      })
    }

    sitemap += `</urlset>`

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    })
  } catch (error) {
    console.error('Error generating blogs sitemap:', error)
    return new NextResponse('Error generating blogs sitemap', { status: 500 })
  }
}
