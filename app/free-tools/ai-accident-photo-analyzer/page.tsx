import type { Metada<PERSON> } from 'next'
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { PhotoAnalyzer } from "@/components/photo-analyzer"

export const metadata: Metadata = {
  title: 'AI Accident Photo Analyzer | Free Tool | Find Car Accident Attorneys',
  description: 'Upload accident photos for AI-powered analysis of damage severity, fault indicators, and repair estimates. Get preliminary insights to support your claim (not legal advice).',
  keywords: 'accident photo analyzer, AI accident analysis, vehicle damage assessment, fault determination, accident evidence analysis, car accident photos AI, insurance claim photos',
  authors: [{ name: 'Find Car Accident Attorneys' }],
  creator: 'Find Car Accident Attorneys',
  publisher: 'Find Car Accident Attorneys',
  metadataBase: new URL('https://findcaraccidentlawyers.org'),
  alternates: {
    canonical: '/free-tools/ai-accident-photo-analyzer',
  },
  openGraph: {
    title: 'AI Accident Photo Analyzer | Free Accident Analysis Tool',
    description: 'Upload accident photos for AI-powered analysis of damage severity, fault indicators, and repair estimates.',
    url: 'https://findcaraccidentlawyers.org/free-tools/ai-accident-photo-analyzer',
    siteName: 'Find Car Accident Attorneys',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Accident Photo Analyzer | Free Tool',
    description: 'AI-powered analysis of accident photos for damage assessment and fault indicators.',
  },
}

export default function PhotoAnalyzerPage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
              AI Accident Photo Analyzer
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Upload photos of your car accident scene for AI-powered analysis of damage severity, fault indicators, and preliminary repair estimates.
            </p>
          </div>

          <PhotoAnalyzer />
        </div>
      </main>
      <Footer />
    </div>
  )
}
