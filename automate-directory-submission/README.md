# Directory Submission Automation Toolkit

This folder contains all tools and information needed to automate directory submissions for "Find Car Accident Attorneys".

## 📁 File Structure

### Core Files
- **`directory-submission-info.md`** - Complete business information template with detailed fill-out instructions for different directory types
- **`test_475b05c7-cff1-44b8-8251-d6a97bbf1dd2.spec.ts`** - 1abc.org submission automation script

### Specialized Tests
- **`geekwire-submission-test.spec.ts`** - Prepared script for GeekWire startup directory (requires geographic qualification check)

### Business Information Summary
```
Company: Find Car Accident Attorneys
Website: https://findcaraccidentlawyers.org/
Email: <EMAIL>
Contact: <PERSON> (Co-founder)
Address: 33 Irving Place, New York, NY 10003, USA
Founded: 2025
Employees: 5
Logo: /public/logo_findcaraccidentlawyers.png
```

## 🔧 Usage Instructions

### Step 1: Review Directory Requirements
```
1. Check geographic restrictions (e.g., GeekWire requires WA/OR/ID/BC)
2. Verify industry focus (business, legal, tech startup)
3. Confirm submission criteria and approval process
```

### Step 2: Select Appropriate Test File
```
- 1abc.org: test_475b05c7-cff1-44b8-8251-d6a97bbf1dd2.spec.ts
- GeekWire: geekwire-submission-test.spec.ts (with geographic check)
- Future: Create new .spec.ts files for additional directories
```

### Step 3: Execute Submission
```
1. Update selectors in the test file based on actual directory form
2. Uncomment the relevant page.fill() commands
3. Run: npx playwright test [filename].spec.ts
4. Monitor console output for success/confirmation
```

### Step 4: Post-Submission Verification
```
1. Wait 2-4 weeks for processing
2. Check directory for new listing
3. Verify all information displays correctly
4. Note submission date for reference
```

## 🎯 Directory Categories Covered

### ✅ Business & General Directories
- Basic business information with professional presentation
- SEO-optimized descriptions for search visibility
- Appropriate categorization and keywords

### 🏢 Tech Startup Directories
- Adapted description emphasizing "legal tech" platform
- Startup metrics (founding year, team size, business model)
- Tech-focused keywords and industry context

### ⚖️ Legal Service Directories
- Practice areas and service specializations
- Attorney/client focused messaging
- Professional accreditations and reviews emphasis

## ⚙️ Automation Scripts

Each test file includes:
- Form navigation automation
- Data population from template
- Submission execution
- Error handling and verification notes
- Directory-specific adaptations

## 🚨 Important Notes

1. **Geographic Restrictions**: Some directories (like GeekWire) have strict location requirements
2. **Manual Review**: Most directories require human approval - expect 2-4 week processing
3. **Selector Updates**: Test selectors may need adjustment based on actual form structure
4. **Rate Limits**: Avoid submitting to multiple directories simultaneously
5. **Verification**: Always verify submissions appear correctly in directory listings

## 📊 Success Metrics

Track your directory presence by monitoring:
- New attorney leads from each directory
- Search rankings for your keywords
- Click-through rates from directory listings
- Overall referral volume attribution

---

**Toolkit Created:** November 9, 2025
**Next Update:** When additional directories are automated
